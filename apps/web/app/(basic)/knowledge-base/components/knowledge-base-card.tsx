"use client"

import { useState } from "react"
import { Trash2, Files } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@ragtop-web/ui/components/card"
import { Button } from "@ragtop-web/ui/components/button"
import { KnowledgeBase } from "@/service/knowledge-base-service"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@ragtop-web/ui/components/alert-dialog"

interface KnowledgeBaseCardProps {
  knowledgeBase: KnowledgeBase
  onClick: () => void
  onDelete: () => void
  onManageFiles: (knowledgeBase: KnowledgeBase) => void
}

export function KnowledgeBaseCard({
  knowledgeBase,
  onClick,
  onDelete,
  onManageFiles
}: KnowledgeBaseCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)

    // 处理打开删除确认对话框
  const handleOpenDeleteDialog = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDeleteDialogOpen(true)
  }
  const handleDelete = async() => {
   await onDelete()
    setIsDeleteDialogOpen(false)
  }


  return (
    <Card
      className="cursor-pointer hover:border-primary/50 transition-colors relative"
      onClick={onClick}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2">
          {knowledgeBase.name}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-sm text-muted-foreground">
          <p>
            <span className="font-medium">文件数量:</span> {knowledgeBase.doc_num || 0} 个文件
          </p>
        </div>
      </CardContent>

      {/* 操作按钮 - 仅在悬停时显示 */}
      {isHovered && (
        <div className="absolute top-2 right-2 flex gap-1">
          {/* 删除按钮 */}
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 text-destructive hover:text-destructive hover:bg-destructive/10"
            onClick={handleOpenDeleteDialog}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )}
       {/* 删除确认对话框 */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除</AlertDialogTitle>
            <AlertDialogDescription>
              您确定要删除团队 "{knowledgeBase?.name}" 吗？此操作无法撤销。
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction onClick={handleDelete}>
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  )
}
